import { useParams, useNavigate, useLocation } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { Country } from '../types'

// 默认美国
const defaultCountry: Country = {
  id: 1,
  name: 'United States',
  code: 'US',
  flag: '🇺🇸',
  merchant_count: 6013,
  status: 1
}

// 支持的国家代码列表
const supportedCountryCodes = [
  'US', 'CA', 'AT', 'DE', 'FR', 'UK', 'ES', 'IT', 'MX', 'IN',
  'PT', 'PL', 'NL', 'AU', 'BE', 'BR', 'CL', 'HK', 'AR', 'FI',
  'SE', 'SG', 'DK', 'NO', 'PE', 'CZ', 'TR', 'IE', 'SA', 'CH',
  'MY', 'RO', 'AE', 'NZ'
]

export const useCountryFromUrl = () => {
  const { country_code } = useParams<{ country_code?: string }>()
  const navigate = useNavigate()
  const location = useLocation()
  const [currentCountry, setCurrentCountry] = useState<Country>(defaultCountry)

  // 验证国家代码是否有效
  const isValidCountryCode = (code: string | undefined): boolean => {
    return code ? supportedCountryCodes.includes(code.toUpperCase()) : false
  }

  // 获取当前选择的国家代码
  const getSelectedCountryCode = (): string => {
    // 1. 优先使用URL中的国家代码
    if (country_code && isValidCountryCode(country_code)) {
      return country_code.toUpperCase()
    }

    // 2. 其次使用localStorage中的国家代码
    const savedCountry = localStorage.getItem('selectedCountry')
    if (savedCountry) {
      try {
        const parsed = JSON.parse(savedCountry)
        if (parsed.code && isValidCountryCode(parsed.code)) {
          return parsed.code.toUpperCase()
        }
      } catch (error) {
        console.error('Error parsing saved country:', error)
      }
    }

    // 3. 默认返回美国
    return 'US'
  }

  // 更新URL中的国家代码
  const updateCountryInUrl = (newCountryCode: string) => {
    const pathSegments = location.pathname.split('/').filter(Boolean)
    const searchParams = location.search

    // 检查当前路径是否已经包含国家代码
    if (pathSegments.length >= 2 && isValidCountryCode(pathSegments[1])) {
      // 替换现有的国家代码
      pathSegments[1] = newCountryCode.toUpperCase()
    } else if (pathSegments.length >= 1) {
      // 在路径中插入国家代码
      pathSegments.splice(1, 0, newCountryCode.toUpperCase())
    }

    const newPath = '/' + pathSegments.join('/') + searchParams
    navigate(newPath, { replace: true })
  }

  // 从URL中移除国家代码
  const removeCountryFromUrl = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean)
    const searchParams = location.search

    // 如果第二个段是国家代码，则移除它
    if (pathSegments.length >= 2 && isValidCountryCode(pathSegments[1])) {
      pathSegments.splice(1, 1)
    }

    const newPath = '/' + pathSegments.join('/') + searchParams
    navigate(newPath, { replace: true })
  }

  // 设置当前国家但不更新URL
  const setCountry = (country: Country) => {
    setCurrentCountry(country)

    // 只保存到localStorage，不更新URL
    localStorage.setItem('selectedCountry', JSON.stringify(country))
  }

  // 初始化时设置国家
  useEffect(() => {
    const selectedCode = getSelectedCountryCode()

    // 设置当前国家（这里简化处理，实际应该从API获取完整信息）
    setCurrentCountry({
      ...defaultCountry,
      code: selectedCode,
      name: selectedCode === 'US' ? 'United States' : selectedCode // 简化处理
    })
  }, [country_code])

  return {
    currentCountry,
    countryCode: getSelectedCountryCode(),
    setCountry,
    isValidCountryCode,
    updateCountryInUrl,
    removeCountryFromUrl
  }
}
