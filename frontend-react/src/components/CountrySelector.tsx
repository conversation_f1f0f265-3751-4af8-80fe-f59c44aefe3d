import React, { useState, useRef, useEffect } from 'react'
import { FaChevronDown } from 'react-icons/fa'
import { useCountryFromUrl } from '../hooks/useCountryFromUrl'

interface CountrySelectorProps {
  className?: string
}

// 简化的国家接口，只包含必要字段
interface SimpleCountry {
  id: number
  name: string
  code: string
  flag: string
  merchant_count: number
}

const CountrySelector: React.FC<CountrySelectorProps> = ({ className = '' }) => {
  const { currentCountry, setCountry } = useCountryFromUrl()
  const [countries, setCountries] = useState<SimpleCountry[]>([])
  const [loading, setLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // 获取国家列表
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        setLoading(true)
        const response = await fetch('http://localhost:8080/api/v1/countries?page=1&page_size=100')
        const data = await response.json()

        if (data.data && data.data.country_list) {
          // 只提取必要的字段，避免渲染问题
          // 只保留商家数量>0的国家
          const activeCountries: SimpleCountry[] = data.data.country_list
            .filter((country: any) => country.merchant_count > 0)
            .map((country: any) => ({
              id: country.id,
              name: country.name,
              code: country.code,
              flag: country.flag,
              merchant_count: country.merchant_count
            }))

          // 排序：美国第一，其他按商家数量降序
          const sortedCountries = activeCountries.sort((a, b) => {
            // 美国始终第一
            if (a.code === 'US') return -1
            if (b.code === 'US') return 1
            // 其他国家按商家数量降序排列
            return b.merchant_count - a.merchant_count
          })

          setCountries(sortedCountries)
        }
      } catch (error) {
        console.error('Error fetching countries:', error)
        // 使用默认国家列表
        setCountries([{
          id: 1,
          name: 'United States',
          code: 'US',
          flag: '🇺🇸',
          merchant_count: 6013
        }])
      } finally {
        setLoading(false)
      }
    }

    fetchCountries()
  }, [])

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleCountrySelect = (country: SimpleCountry) => {
    // 转换为完整的Country对象
    const fullCountry = {
      id: country.id,
      name: country.name,
      code: country.code,
      flag: country.flag,
      merchant_count: country.merchant_count,
      status: 1
    }

    // 使用setCountry方法更新状态和localStorage
    setCountry(fullCountry)
    setIsOpen(false)

    // 触发页面刷新以更新数据
    window.location.reload()
  }

  if (loading || !currentCountry) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-6 h-4 bg-gray-200 rounded animate-pulse"></div>
        <span className="text-sm text-gray-500">Loading...</span>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
      >
        <span className="text-lg">{currentCountry.flag || '🇺🇸'}</span>
        <span className="hidden sm:inline">{currentCountry.name || 'United States'}</span>
        <span className="sm:hidden">{currentCountry.code || 'US'}</span>
        <FaChevronDown
          className={`w-3 h-3 transition-transform ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {isOpen && countries.length > 0 && (
        <div className="absolute right-0 z-50 mt-2 w-64 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
          <div className="py-1">
            {countries.map((country) => (
              <button
                key={country.id}
                onClick={() => handleCountrySelect(country)}
                className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center space-x-3 ${
                  currentCountry.id === country.id ? 'bg-purple-50 text-purple-700' : 'text-gray-700'
                }`}
              >
                <span className="text-lg">{country.flag || '🏳️'}</span>
                <div className="flex-1">
                  <div className="font-medium">{country.name || 'Unknown'}</div>
                  <div className="text-xs text-gray-500">
                    {country.code || 'XX'} • {country.merchant_count} stores
                  </div>
                </div>
                {currentCountry.id === country.id && (
                  <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default CountrySelector
